# KingStar回调重试机制优化

## 问题描述

KingStar接口调用后会立即回调，可能会导致以下问题：
1. **时序问题**：回调到达时，数据库事务可能还没有提交
2. **数据不存在**：`priceApplyEntity`可能不存在或查询不到
3. **数据不完整**：申请单数据可能没有及时更新
4. **数据覆盖**：可能导致数据被覆盖

## 解决方案

### 1. 增强重试机制

#### 配置参数
在配置文件中添加以下参数：

```yaml
kingstar:
  callback:
    retry:
      maxAttempts: 5          # 最大重试次数，默认5次
      baseDelayMs: 50         # 基础延迟时间(毫秒)，默认50ms
      maxDelayMs: 1000        # 最大延迟时间(毫秒)，默认1000ms
```

#### 重试策略
- **指数退避算法**：延迟时间按指数增长，避免频繁重试
- **最大延迟限制**：防止延迟时间过长
- **数据完整性验证**：确保查询到的数据完整

#### 延迟时间计算
```
delayMs = min(baseDelayMs * 2^(attempt-1), maxDelayMs)
```

示例：
- 第1次重试：50ms
- 第2次重试：100ms  
- 第3次重试：200ms
- 第4次重试：400ms
- 第5次重试：800ms

### 2. 数据完整性验证

在重试过程中验证申请单数据的完整性：
- 检查关键字段是否存在（ID、Code、Status、CustomerId）
- 如果数据不完整，继续重试
- 记录详细的日志信息

### 3. 代码实现

#### 主要方法

1. **getPriceApplyWithRetry(String instructId)**
   - 使用配置化的重试参数
   - 实现指数退避算法
   - 添加数据完整性验证

2. **isValidPriceApplyEntity(PriceApplyEntity entity)**
   - 验证申请单数据完整性
   - 记录不完整数据的详细信息

#### 使用位置
- `handleInstructCallback`：处理KingStar回调
- `reSyncByRequestId`：重新同步请求

### 4. 监控和日志

#### 日志记录
- 每次重试的详细信息
- 数据完整性验证结果
- 总耗时统计
- 失败原因分析

#### 监控指标
- 重试成功率
- 平均重试次数
- 平均响应时间
- 数据完整性问题频率

## 配置示例

### 开发环境
```yaml
kingstar:
  callback:
    retry:
      maxAttempts: 3
      baseDelayMs: 30
      maxDelayMs: 500
```

### 生产环境
```yaml
kingstar:
  callback:
    retry:
      maxAttempts: 5
      baseDelayMs: 50
      maxDelayMs: 1000
```

## 使用建议

1. **根据环境调整参数**：开发环境可以使用较小的重试次数和延迟
2. **监控重试频率**：如果重试频率过高，需要检查数据库事务提交时间
3. **定期检查日志**：关注数据完整性问题的频率
4. **性能测试**：在高并发场景下测试重试机制的性能影响

## 注意事项

1. **线程中断处理**：重试过程中正确处理线程中断
2. **资源释放**：确保重试失败时正确释放资源
3. **并发控制**：Redis锁机制防止重复处理
4. **事务边界**：注意数据库事务的边界和提交时机

## 后续优化建议

1. **异步处理**：考虑使用消息队列异步处理回调
2. **数据库优化**：优化数据库查询性能
3. **缓存机制**：添加适当的缓存减少数据库查询
4. **监控告警**：添加重试失败的监控告警
