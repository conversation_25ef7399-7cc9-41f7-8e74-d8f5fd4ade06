# Azure Service Bus 延迟消息优化方案

## 概述

本次优化将原有的基于 `ScheduledExecutorService` 的延迟消息实现替换为 Azure Service Bus 原生的 Scheduled Messages 功能，解决了以下问题：

### 原有问题
1. **内存占用**：使用 `ScheduledExecutorService` 会在应用内存中保持定时任务
2. **服务重启丢失**：应用重启后，所有未执行的定时任务会丢失
3. **集群问题**：多实例部署时会重复执行定时任务
4. **可靠性差**：没有持久化机制

### 优化后的优势
1. **可靠性高**：消息持久化存储，不会因服务重启丢失
2. **性能好**：不占用应用内存，由 Azure Service Bus 管理
3. **集群友好**：多实例部署时不会重复执行
4. **功能完整**：支持取消、查询等高级功能
5. **精确投递**：Azure Service Bus 保证在指定时间精确投递

## 主要变更

### 1. 依赖更新
在 `pom.xml` 中添加了 Azure Service Bus SDK：
```xml
<dependency>
    <groupId>com.azure</groupId>
    <artifactId>azure-messaging-servicebus</artifactId>
    <version>7.14.4</version>
</dependency>
```

### 2. 配置类
- `ServiceBusConfig.java`：Azure Service Bus 客户端配置
- `ScheduledTaskConfig.java`：定时清理任务配置

### 3. 服务类优化
- `ScheduledMessageService.java`：核心服务类，使用 Azure Service Bus 原生功能
- `ScheduledMessageRetryService.java`：重试机制服务

### 4. 新增功能
- 消息取消功能
- 消息数量监控
- 重试机制
- 定期清理

## 使用方法

### 基本用法
```java
@Autowired
private ScheduledMessageService scheduledMessageService;

// 发送定时消息
Map<String, Object> messageData = scheduledMessageService.createContractQueryMessage(
    "businessEntity", "contractCode", "getContractOpenQuantity");
String messageId = scheduledMessageService.sendScheduledMessage(
    "queueName", messageData, "2025-12-31 23:59:59");

// 取消定时消息
boolean cancelled = scheduledMessageService.cancelScheduledMessage(messageId);

// 查询已调度消息数量
int count = scheduledMessageService.getScheduledMessageCount();
```

### API 接口
1. **发送定时查询**：`GET /getContractOpenQuantityScheduled`
2. **取消定时查询**：`GET /cancelScheduledQuery?messageId={messageId}`
3. **查询消息数量**：`GET /getScheduledMessageCount`

### 配置要求
在配置文件中添加 Azure Service Bus 连接字符串：
```yaml
spring:
  jms:
    servicebus:
      connection-string: Endpoint=sb://your-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-key
```

## 部署注意事项

### 1. 环境变量
确保设置以下环境变量：
- `AZURE_SERVICEBUS_CONNECTION_STRING`：Azure Service Bus 连接字符串
- `ATLAS_SYNC_QUEUE_NAME`：队列名称

### 2. 权限配置
确保 Azure Service Bus 连接字符串具有以下权限：
- Send：发送消息
- Listen：接收消息
- Manage：管理定时消息（取消等）

### 3. 监控
- 通过 `/getScheduledMessageCount` 接口监控已调度消息数量
- 查看应用日志了解消息发送和取消情况
- 在 Azure Portal 中监控 Service Bus 队列状态

## 测试

运行单元测试：
```bash
mvn test -Dtest=ScheduledMessageServiceTest
```

## 回滚方案

如果需要回滚到原有实现：
1. 移除 Azure Service Bus SDK 依赖
2. 恢复原有的 `ScheduledMessageService` 实现
3. 移除新增的配置类

## 性能对比

| 指标 | 原有实现 | 优化后实现 |
|------|----------|------------|
| 内存占用 | 高（每个定时任务占用内存） | 低（无内存占用） |
| 可靠性 | 低（重启丢失） | 高（持久化存储） |
| 集群支持 | 差（重复执行） | 好（原生支持） |
| 功能完整性 | 基础 | 完整（支持取消、监控） |
| 维护成本 | 高 | 低 |

## 常见问题

### Q: 如何处理 Azure Service Bus 连接失败？
A: 服务包含重试机制和回退到 JMS 的逻辑，确保消息不会丢失。

### Q: 定时消息的精度如何？
A: Azure Service Bus 的定时消息精度为秒级，满足业务需求。

### Q: 如何监控定时消息的执行情况？
A: 可以通过应用日志、Azure Portal 和提供的监控接口进行监控。

### Q: 是否支持重复定时消息？
A: Azure Service Bus 不支持重复定时消息，如需要可以在消息处理完成后重新调度。
