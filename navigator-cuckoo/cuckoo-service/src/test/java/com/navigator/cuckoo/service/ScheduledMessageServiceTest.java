package com.navigator.cuckoo.service;

import com.azure.messaging.servicebus.ServiceBusMessage;
import com.azure.messaging.servicebus.ServiceBusSenderClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jms.core.JmsTemplate;

import java.time.OffsetDateTime;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ScheduledMessageService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class ScheduledMessageServiceTest {

    @Mock
    private JmsTemplate jmsTemplate;

    @Mock
    private ServiceBusSenderClient serviceBusSenderClient;

    private ScheduledMessageService scheduledMessageService;

    @BeforeEach
    void setUp() {
        scheduledMessageService = new ScheduledMessageService(jmsTemplate, serviceBusSenderClient);
    }

    @Test
    void testSendScheduledMessage_FutureTime() {
        // 准备测试数据
        String queueName = "test-queue";
        Map<String, Object> messageData = Map.of(
            "action", "getContractOpenQuantity",
            "businessEntity", "TEST_ENTITY",
            "contractCode", "TEST_CONTRACT"
        );
        String scheduledTime = "2025-12-31 23:59:59";
        
        // Mock Service Bus 返回序列号
        when(serviceBusSenderClient.scheduleMessage(any(ServiceBusMessage.class), any(OffsetDateTime.class)))
            .thenReturn(12345L);

        // 执行测试
        String messageId = scheduledMessageService.sendScheduledMessage(queueName, messageData, scheduledTime);

        // 验证结果
        assertNotNull(messageId);
        assertTrue(messageId.contains("getContractOpenQuantity"));
        
        // 验证 Service Bus 被调用
        verify(serviceBusSenderClient, times(1))
            .scheduleMessage(any(ServiceBusMessage.class), any(OffsetDateTime.class));
        
        // 验证 JMS 没有被调用（因为是定时消息）
        verify(jmsTemplate, never()).convertAndSend(anyString(), anyString());
    }

    @Test
    void testSendScheduledMessage_PastTime() {
        // 准备测试数据
        String queueName = "test-queue";
        Map<String, Object> messageData = Map.of(
            "action", "getContractOpenQuantity",
            "businessEntity", "TEST_ENTITY",
            "contractCode", "TEST_CONTRACT"
        );
        String scheduledTime = "2020-01-01 00:00:00"; // 过去的时间

        // 执行测试
        String messageId = scheduledMessageService.sendScheduledMessage(queueName, messageData, scheduledTime);

        // 验证结果
        assertNotNull(messageId);
        
        // 验证立即发送被调用
        verify(serviceBusSenderClient, times(1)).sendMessage(any(ServiceBusMessage.class));
    }

    @Test
    void testSendScheduledMessage_ImmediateDelivery() {
        // 准备测试数据
        String queueName = "test-queue";
        Map<String, Object> messageData = Map.of(
            "action", "getContractOpenQuantity",
            "businessEntity", "TEST_ENTITY",
            "contractCode", "TEST_CONTRACT"
        );
        String scheduledTime = null; // 立即投递

        // 执行测试
        String messageId = scheduledMessageService.sendScheduledMessage(queueName, messageData, scheduledTime);

        // 验证结果
        assertNotNull(messageId);
        
        // 验证立即发送被调用
        verify(serviceBusSenderClient, times(1)).sendMessage(any(ServiceBusMessage.class));
        
        // 验证定时发送没有被调用
        verify(serviceBusSenderClient, never())
            .scheduleMessage(any(ServiceBusMessage.class), any(OffsetDateTime.class));
    }

    @Test
    void testCancelScheduledMessage_Success() {
        // 先发送一个定时消息
        String queueName = "test-queue";
        Map<String, Object> messageData = Map.of("action", "test");
        String scheduledTime = "2025-12-31 23:59:59";
        
        when(serviceBusSenderClient.scheduleMessage(any(ServiceBusMessage.class), any(OffsetDateTime.class)))
            .thenReturn(12345L);
        
        String messageId = scheduledMessageService.sendScheduledMessage(queueName, messageData, scheduledTime);

        // 取消消息
        boolean result = scheduledMessageService.cancelScheduledMessage(messageId);

        // 验证结果
        assertTrue(result);
        verify(serviceBusSenderClient, times(1)).cancelScheduledMessage(12345L);
    }

    @Test
    void testCancelScheduledMessage_NotFound() {
        // 尝试取消不存在的消息
        boolean result = scheduledMessageService.cancelScheduledMessage("non-existent-id");

        // 验证结果
        assertFalse(result);
        verify(serviceBusSenderClient, never()).cancelScheduledMessage(anyLong());
    }

    @Test
    void testCreateContractQueryMessage() {
        // 执行测试
        Map<String, Object> result = scheduledMessageService.createContractQueryMessage(
            "TEST_ENTITY", "TEST_CONTRACT", "getContractOpenQuantity");

        // 验证结果
        assertEquals("getContractOpenQuantity", result.get("action"));
        assertEquals("TEST_ENTITY", result.get("businessEntity"));
        assertEquals("TEST_CONTRACT", result.get("contractCode"));
        assertNotNull(result.get("timestamp"));
        assertNotNull(result.get("messageId"));
    }

    @Test
    void testGetScheduledMessageCount() {
        // 初始状态应该为0
        assertEquals(0, scheduledMessageService.getScheduledMessageCount());

        // 发送一个定时消息
        String queueName = "test-queue";
        Map<String, Object> messageData = Map.of("action", "test");
        String scheduledTime = "2025-12-31 23:59:59";
        
        when(serviceBusSenderClient.scheduleMessage(any(ServiceBusMessage.class), any(OffsetDateTime.class)))
            .thenReturn(12345L);
        
        scheduledMessageService.sendScheduledMessage(queueName, messageData, scheduledTime);

        // 验证计数增加
        assertEquals(1, scheduledMessageService.getScheduledMessageCount());
    }
}
