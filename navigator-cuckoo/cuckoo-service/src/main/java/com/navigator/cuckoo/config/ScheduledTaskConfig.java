package com.navigator.cuckoo.config;

import com.navigator.cuckoo.service.ScheduledMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 定时任务配置
 * 
 * <AUTHOR>
 * @since 2025-1-7
 */
@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
public class ScheduledTaskConfig {

    private final ScheduledMessageService scheduledMessageService;

    /**
     * 定期清理过期的消息记录
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void cleanupExpiredMessages() {
        try {
            scheduledMessageService.cleanupExpiredMessages();
        } catch (Exception e) {
            log.error("清理过期消息记录时发生错误: {}", e.getMessage(), e);
        }
    }
}
