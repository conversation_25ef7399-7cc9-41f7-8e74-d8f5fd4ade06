package com.navigator.cuckoo.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

/**
 * 定时消息重试服务
 * 提供重试机制以处理 Azure Service Bus 的临时故障
 * 
 * <AUTHOR>
 * @since 2025-1-7
 */
@Slf4j
@Service
@EnableRetry
@RequiredArgsConstructor
public class ScheduledMessageRetryService {

    private final ScheduledMessageService scheduledMessageService;

    /**
     * 带重试机制的发送定时消息
     * 
     * @param queueName 队列名称
     * @param messageData 消息数据
     * @param scheduledTime 定时投递时间
     * @return 消息ID
     */
    @Retryable(
        value = {Exception.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public String sendScheduledMessageWithRetry(String queueName, Object messageData, String scheduledTime) {
        try {
            return scheduledMessageService.sendScheduledMessage(queueName, messageData, scheduledTime);
        } catch (Exception e) {
            log.warn("发送定时消息失败，将进行重试: {}", e.getMessage());
            throw e; // 重新抛出异常以触发重试
        }
    }

    /**
     * 带重试机制的取消定时消息
     * 
     * @param messageId 消息ID
     * @return 是否成功取消
     */
    @Retryable(
        value = {Exception.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 500, multiplier = 2)
    )
    public boolean cancelScheduledMessageWithRetry(String messageId) {
        try {
            return scheduledMessageService.cancelScheduledMessage(messageId);
        } catch (Exception e) {
            log.warn("取消定时消息失败，将进行重试: {}", e.getMessage());
            throw e; // 重新抛出异常以触发重试
        }
    }
}
