# Azure Service Bus 配置示例
# 请根据实际环境配置相应的连接字符串

spring:
  jms:
    servicebus:
      # Azure Service Bus 连接字符串
      # 格式: Endpoint=sb://your-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-key
      connection-string: ${AZURE_SERVICEBUS_CONNECTION_STRING:Endpoint=sb://your-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-key}
      
      # 连接池配置
      pool:
        enabled: true
        max-connections: 10
        idle-timeout: 30000
        
      # 重试配置
      retry:
        max-retries: 3
        retry-delay: 1000

# 消息队列配置
messageQueue:
  atlas:
    syncQueueName: ${ATLAS_SYNC_QUEUE_NAME:atlas-sync-queue}
    syncDeadLetterName: ${ATLAS_SYNC_DLQ_NAME:atlas-sync-dlq}

# 日志配置
logging:
  level:
    com.navigator.cuckoo.service.ScheduledMessageService: DEBUG
    com.azure.messaging.servicebus: INFO
